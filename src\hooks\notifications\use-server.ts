import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
// import { apiUrl } from "@/utils/urls"; // Commented out for mock implementation

// Mock notification settings data - realistic data following established schema
const MOCK_NOTIFICATION_SETTINGS: INotificationSetting[] = [
  {
    id: "1",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "document_reminder",
    channels: ["email", "in_app"],
    isEnabled: true,
    customSchedule: 24,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "2",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "application_submitted",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "3",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "agent_assigned",
    channels: ["email", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "4",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "status_update",
    channels: ["email", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "5",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "agent_query",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "6",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "document_rejected",
    channels: ["email", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "7",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "authority_query",
    channels: ["email", "in_app"],
    isEnabled: false,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "8",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "deadline_warning",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    customSchedule: 48,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "9",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "missing_document",
    channels: ["email", "in_app"],
    isEnabled: true,
    customSchedule: 72,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "10",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "eligibility_confirmation",
    channels: ["email", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "11",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "payment_confirmation",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "12",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "final_decision",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "13",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "system_maintenance",
    channels: ["email", "in_app"],
    isEnabled: false,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "14",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "escalation_notice",
    channels: ["email", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
];

// Get Notification Settings - MOCK IMPLEMENTATION
// TODO: Replace with actual backend API call when backend is ready
export const getNotificationSettings = async (userType?: string) => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  // Simulate API delay for realistic behavior
  await new Promise((resolve) => setTimeout(resolve, 300));

  // Filter by userType if provided (for future compatibility)
  let filteredSettings = MOCK_NOTIFICATION_SETTINGS;
  if (userType) {
    filteredSettings = MOCK_NOTIFICATION_SETTINGS.filter(
      (setting) => setting.userType === userType
    );
  }

  // Return mock data in the same format as the real API
  return { data: filteredSettings };
};
