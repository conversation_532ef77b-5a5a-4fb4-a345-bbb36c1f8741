import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

// Force dynamic rendering to prevent static generation errors when using headers()
// This is required because getServerSession() uses headers() internally
export const dynamic = "force-dynamic";

/**
 * GET /api/notifications/settings
 *
 * Retrieves notification settings for the authenticated user.
 * Supports optional userType query parameter for filtering.
 *
 * @param {NextRequest} request Next.js request object containing optional query parameters
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user session
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Authentication required. Please log in to access notification settings.",
        },
        { status: 401 }
      );
    }

    // Extract optional userType parameter for filtering
    const { searchParams } = new URL(request.url);
    const userType = searchParams.get("userType");

    // Build query parameters for backend API
    const params = new URLSearchParams();
    if (userType) params.append("userType", userType);

    const backendUrl = `${apiUrl}/notifications/settings?${params.toString()}`;

    // Make authenticated request to backend API
    const response = await fetch(backendUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle backend errors with user-friendly messages
      const errorMessage =
        data.message ||
        "Unable to retrieve notification settings. Please try again later.";
      return NextResponse.json(
        {
          success: false,
          message: errorMessage,
        },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    // Log error for debugging while providing user-friendly message
    console.error("Error in GET /api/notifications/settings:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "A technical error occurred while retrieving notification settings. Please try again or contact support if the issue persists.",
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/notifications/settings
 *
 * Updates notification settings for the authenticated user.
 * Accepts an array of notification settings in the request body.
 *
 * @param {NextRequest} request Next.js request object containing settings data
 */
export async function PUT(request: NextRequest) {
  try {
    // Authenticate user session
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Authentication required. Please log in to update notification settings.",
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();

    // Update notification settings in backend
    const backendUrl = `${apiUrl}/notifications/settings`;

    const response = await fetch(backendUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle backend errors with user-friendly messages
      const errorMessage =
        data.message ||
        "Unable to save notification settings. Please try again later.";
      return NextResponse.json(
        {
          success: false,
          message: errorMessage,
        },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    // Log error for debugging while providing user-friendly message
    console.error("Error in PUT /api/notifications/settings:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "A technical error occurred while saving notification settings. Please try again or contact support if the issue persists.",
      },
      { status: 500 }
    );
  }
}
